"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/libs/utils";
import {
  useGroupAddMembersMutation,
  useGroupMembers,
  useGroupRemoveMemberMutation,
} from "@/queries/groups.queries";
import { useOrganizationMembers } from "@/queries/organization.queries";
import type { GroupOutput } from "@/types/group.types";
import type { OrganizationMember } from "@/types/organization.types";
import { getInitials } from "@/utils/get-initials";
import { getMemberRoleBadgeVariant } from "@/utils/get-member-role-badge-variant";
import { IconSearch, IconX } from "@tabler/icons-react";
import { useMemo, useState } from "react";

interface ManageGroupMembersDialogProps {
  open: boolean;
  onClose: () => void;
  group: GroupOutput;
  organizationId: string;
}

export function ManageGroupMembersDialog({
  open,
  onClose,
  group,
  organizationId,
}: ManageGroupMembersDialogProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMemberIds, setSelectedMemberIds] = useState<Set<string>>(
    new Set(),
  );

  const { data: allMembers, isLoading: membersLoading } =
    useOrganizationMembers(organizationId);
  const { data: groupMembers, isLoading: groupMembersLoading } =
    useGroupMembers({ groupId: group.id, take: 500 });

  const addMembersMutation = useGroupAddMembersMutation();
  const removeMemberMutation = useGroupRemoveMemberMutation();

  // Get current group member IDs
  const currentGroupMemberIds = useMemo(() => {
    return new Set(groupMembers?.data?.map((gm) => gm.member.id) ?? []);
  }, [groupMembers]);

  // Filter members based on search query
  const filteredMembers = useMemo(() => {
    if (!allMembers) return [];

    return allMembers.filter(
      (member: OrganizationMember) =>
        member.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        member.user.email.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [allMembers, searchQuery]);

  const handleMemberToggle = (memberId: string) => {
    const newSelected = new Set(selectedMemberIds);
    if (newSelected.has(memberId)) {
      newSelected.delete(memberId);
    } else {
      newSelected.add(memberId);
    }
    setSelectedMemberIds(newSelected);
  };

  const handleAddSelectedMembers = async () => {
    if (selectedMemberIds.size === 0) return;

    await addMembersMutation.mutateAsync({
      organizationId,
      groupId: group.id,
      memberIds: Array.from(selectedMemberIds),
    });
    setSelectedMemberIds(new Set());
  };

  const handleRemoveMember = async (memberId: string) => {
    await removeMemberMutation.mutateAsync({
      organizationId,
      groupId: group.id,
      memberId,
    });
  };

  const isLoading = membersLoading || groupMembersLoading;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Manage Group Members</DialogTitle>
          <DialogDescription>
            Add or remove members from the &quot;{group.name}&quot; group.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader />
          </div>
        ) : (
          <div className="space-y-4">
            {/* Current Group Members */}
            <div>
              <h4 className="mb-3 text-sm font-medium">
                Current Members ({groupMembers?.data?.length ?? 0})
              </h4>
              {groupMembers?.data?.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No members in this group yet.
                </p>
              ) : (
                <ScrollArea className="max-h-80">
                  <div className="space-y-2">
                    {groupMembers?.data?.map((groupMember) => (
                      <div
                        key={groupMember.id}
                        className="flex items-center justify-between rounded-lg border p-3"
                      >
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage
                              src={groupMember.member.user.image || ""}
                            />
                            <AvatarFallback>
                              {getInitials(groupMember.member.user.email, 1)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">
                              {groupMember.member.user.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {groupMember.member.user.email}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={getMemberRoleBadgeVariant(
                              groupMember.member.role || "member",
                            )}
                            className="capitalize"
                          >
                            {groupMember.member.role || "member"}
                          </Badge>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() =>
                              handleRemoveMember(groupMember.member.id)
                            }
                            disabled={removeMemberMutation.isPending}
                            className="size-7"
                          >
                            <IconX size={14} />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>

            <Separator />

            {/* Add New Members */}
            <div>
              <div className="mb-3 flex items-center justify-between">
                <h4 className="text-sm font-medium">Add Members</h4>
                <span
                  className={cn(
                    "text-xs text-muted-foreground",
                    selectedMemberIds.size === 0 && "invisible",
                  )}
                >
                  {selectedMemberIds.size} selected
                </span>
              </div>

              {/* Search */}
              <div className="mb-3">
                <Input
                  placeholder="Search members..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  icon={<IconSearch size={16} />}
                />
              </div>

              {/* Members List */}
              <ScrollArea className="max-h-80">
                <div className="space-y-2">
                  {filteredMembers
                    .filter(
                      (member: OrganizationMember) =>
                        !currentGroupMemberIds.has(member.id),
                    )
                    .map((member: OrganizationMember) => (
                      <div
                        key={member.id}
                        role="button"
                        tabIndex={0}
                        onClick={() => handleMemberToggle(member.id)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") {
                            e.preventDefault();
                            handleMemberToggle(member.id);
                          }
                        }}
                        aria-pressed={selectedMemberIds.has(member.id)}
                        className={cn(
                          "flex cursor-pointer items-center justify-between rounded-lg border p-3 hover:bg-muted",
                          selectedMemberIds.has(member.id) && "bg-muted",
                        )}
                      >
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            checked={selectedMemberIds.has(member.id)}
                            onCheckedChange={() =>
                              handleMemberToggle(member.id)
                            }
                            onClick={(e) => e.stopPropagation()}
                          />
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.user.image || ""} />
                            <AvatarFallback>
                              {getInitials(member.user.email, 1)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">
                              {member.user.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {member.user.email}
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant={getMemberRoleBadgeVariant(member.role)}
                          className="capitalize"
                        >
                          {member.role}
                        </Badge>
                      </div>
                    ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button
            onClick={handleAddSelectedMembers}
            disabled={selectedMemberIds.size === 0}
            loading={addMembersMutation.isPending}
          >
            Add members
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
